from fastapi import APIRouter, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.staticfiles import StaticFiles

from app.core.logging import logger
from app.models.chat import ChatModel
from app.models.error import ErrorResponse
from app.models.embedding import EmbeddingRequest
from app.models.file import FileShareRequest
from app.models.image import ImageRequest, ImageSimilarity
from app.core.config import PIPELINE_VOICE, SHARED_FILES_DIR
from app.models.config import ModelConfig, UpdateChatModelsRequest, UpdateModelRequest, VoiceRequest
from app.services.ollama import OllamaService
from app.services.image import ImageGenerationService, ImageGenerationManager
from app.services.embedding import EmbeddingService, EmbeddingManager
from app.services.config import ConfigService
from app.services.file import FileService
from app.utils.stable_diffusion import process_images_from_url
from app.utils.image_torch import ImageUtils
import base64
import io
from PIL import Image  # <PERSON>ần cài đặt thư viện Pillow: pip install Pillow
import soundfile as sf
from kokoro import KPipeline
import uuid
import os

# Create API router
router = APIRouter()

# Initialize services
ollama_service = None
image_service = None
embedding_service = None
image_manager = None
embedding_manager = None
config_service = ConfigService()


def initialize_services(model_managers):
    """Initialize the services with model managers"""
    global ollama_service, image_service, embedding_service, image_manager, embedding_manager

    # Initialize managers
    image_manager = ImageGenerationManager()
    embedding_manager = EmbeddingManager()
    image_utils = ImageUtils()
    # Initialize services
    ollama_service = OllamaService(model_managers)
    image_service = ImageGenerationService(image_manager, image_utils)
    embedding_service = EmbeddingService(embedding_manager)


@router.post("/chat")
async def chat(item: ChatModel):
    """
    Chat endpoint that routes requests to the least loaded model
    """
    try:
        if ollama_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        response = await ollama_service.send_chat_request(item.messages)
        if "<think>" in response["message"]["content"]:
            think_content = response["message"]["content"].split("</think>")[0]
            response["message"]["content"] = response["message"]["content"].split("</think>")[-1]
            response["message"]["think"] = think_content.replace("<think>", "")
        return JSONResponse(content=response, status_code=200)
    except RuntimeError as e:
        logger.error(f"Runtime error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/generate-image")
async def generate_image(item: ChatModel):
    """
    Generate image endpoint that uses a specific model for image generation
    """
    try:
        if image_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        response = await image_service.generate_image(item.messages)
        return JSONResponse(content=response, status_code=200)
    except RuntimeError as e:
        logger.error(f"Runtime error in generate-image endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in generate-image endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/embeddings")
async def generate_embeddings(item: EmbeddingRequest):
    """
    Generate embeddings for the given messages
    """
    try:
        if embedding_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        response = await embedding_service.generate_embedding(item.messages, item.model)
        return JSONResponse(content=response, status_code=200)
    except RuntimeError as e:
        logger.error(f"Runtime error in embeddings endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in embeddings endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.get("/usage")
async def get_model_usage():
    """
    Get current usage statistics for all models
    """
    try:
        if ollama_service is None or image_service is None or embedding_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        # Get chat model usage
        chat_usage = ollama_service.get_model_usage()

        # Get image model usage
        image_usage = image_service.get_usage()

        # Get embedding model usage
        embedding_usage = embedding_service.get_usage()

        return JSONResponse(
            content={
                "chat": chat_usage,
                "image": image_usage,
                "embedding": embedding_usage
            },
            status_code=200
        )
    except Exception as e:
        logger.error(f"Unexpected error in usage endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.get("/models/config")
async def get_model_config():
    """
    Get the current model configuration
    """
    try:
        config = config_service.get_model_config()
        return JSONResponse(content=config, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in get_model_config endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.put("/models/chat")
async def update_chat_models(request: UpdateChatModelsRequest):
    """
    Update the chat models list
    """
    try:
        result = config_service.update_chat_models(request.operation, request.models)

        # If we have an active ollama service, we need to reinitialize it
        if ollama_service is not None:
            # Get the updated model list
            from app.core.config import MODEL_LIST
            # Create new model managers
            from app.services.ollama import OllamaManager
            model_managers = [OllamaManager(model) for model in MODEL_LIST]
            # Reinitialize the service
            initialize_services(model_managers)

        return JSONResponse(content=result, status_code=200)
    except ValueError as e:
        logger.error(f"Value error in update_chat_models endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in update_chat_models endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.put("/models/image")
async def update_image_model(request: UpdateModelRequest):
    """
    Update the image generation model
    """
    try:
        result = config_service.update_image_model(request.model)

        # If we have an active image service, we need to update its model
        if image_manager is not None:
            image_manager.model = request.model

        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in update_image_model endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.put("/models/embedding")
async def update_embedding_model(request: UpdateModelRequest):
    """
    Update the text embedding model
    """
    try:
        result = config_service.update_embedding_model(request.model)

        # If we have an active embedding service, we need to update its model
        if embedding_manager is not None:
            embedding_manager.model = request.model

        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in update_embedding_model endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.get("/health")
async def health_check():
    import asyncio
    import uuid
    print("start: ", uuid.uuid4())
    await asyncio.sleep(10)
    print("Done: ", uuid.uuid4())
    """Health check endpoint"""
    return {"status": "healthy"}


@router.post("/generate-voice")
async def generate_voice(request: VoiceRequest):
    try:
        request.content = request.content.replace("\n", " ")
        file_id = str(uuid.uuid4())
        if request.voice_id.startswith("br"):
            pipeline = KPipeline(lang_code='b')
            generator = pipeline(request.content, voice=request.voice_id)
        else:
            generator = PIPELINE_VOICE(request.content, voice=request.voice_id)

        # Ensure the shared files directory exists
        os.makedirs(SHARED_FILES_DIR, exist_ok=True)

        # Generate the file path
        file_path = os.path.join(SHARED_FILES_DIR, f'{file_id}.wav')

        for i, (gs, ps, audio) in enumerate(generator):
            sf.write(file_path, audio, 24000)

        # Generate the URL for accessing the file
        from app.core.config import BASE_URL
        file_url = f"{BASE_URL}/share/{file_id}.wav"

        return JSONResponse(
            content={
                "filename": f'{file_id}.wav',
                "url": file_url
            },
            status_code=200
        )
    except Exception as e:
        logger.error(f"Unexpected error in generate_voice endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/share-file")
async def share_file(request: FileShareRequest):
    """
    Share a file and get a URL to access it
    """
    try:
        # Use the file service to save the file
        file_service = FileService()
        result = file_service.save_shared_file(
            content=request.content,
            filename=request.filename,
            file_type=request.file_type
        )

        return JSONResponse(content=result, status_code=200)
    except RuntimeError as e:
        logger.error(f"Runtime error in share_file endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in share_file endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/stable-diffusion")
async def generate_image_stable_diffusion(request: ImageRequest):
    """
    Generate image using Stable Diffusion API endpoint
    """
    try:
        url_endpoint = os.environ.get("ENDPOINT_GEN_IMAGE", "")
        if not url_endpoint:
            raise HTTPException(status_code=500, detail="ENDPOINT_GEN_IMAGE environment variable not set")

        request_body = request.model_dump()
        list_file = await process_images_from_url(url_endpoint, request_body)
        from app.core.config import BASE_URL
        return JSONResponse(content={
            "data": list_file,
            "url": f"{BASE_URL}/share/{list_file[0]}"
        }, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in stable-diffusion endpoint: {str(e)}")
        return JSONResponse(content={"msg": f"Error {e}"}, status_code=500)


@router.post("/clc-similarity")
async def clc_similarity(request: ImageSimilarity):
    try:
        similarity = await image_service.clc_similarity_image(request.image_compare, request.image_red)
        return JSONResponse(content={"data": {"similarity": float(similarity)}}, status_code=200)
    except Exception as e:
        return JSONResponse(content={"error": f"error: {e}"}, status_code=500)
