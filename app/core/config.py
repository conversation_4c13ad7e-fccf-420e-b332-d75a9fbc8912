from typing import List
from kokoro import KPipeline
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API Configuration
URL_ENDPOINT = os.getenv("OLLAMA_API_URL", "http://*************:11434/api/chat")
EMBEDDING_ENDPOINT = os.getenv("OLLAMA_EMBEDDING_URL", "http://*************:11434/api/embeddings")

# Model Configuration
MODEL_LIST = [
    "hf.co/mradermacher/Tifa-Deepsex-14b-CoT-GGUF:Q8_0",
    "hf.co/mradermacher/The-Omega-Directive-Qwen3-14B-v1.1-GGUF:Q8_0",
    "hf.co/mradermacher/Humanish-Roleplay-Llama-3.1-8B-i1-GGUF:Q5_K_M"
]

MODEL_GEN_IMAGE = os.getenv("IMAGE_MODEL", "hf.co/likewendy/Qwen2.5-14B-lora-sex-v2-q4_k_m:Q4_K_M")
MODEL_EMBEDDING = os.getenv("EMBEDDING_MODEL", "nomic-embed-text")

# Server Configuration
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", "8010"))
BASE_URL = os.getenv("BASE_URL", f"http://{HOST}:{PORT}")

# File Sharing Configuration
SHARED_FILES_DIR = os.getenv("SHARED_FILES_DIR", "shared_files")

# Rate Limiting Configuration
MAX_TPM = int(os.getenv("MAX_TPM", "500000"))  # Maximum tokens per minute
MAX_RPM = int(os.getenv("MAX_RPM", "600"))     # Maximum requests per minute
PIPELINE_VOICE = KPipeline(lang_code='a')
