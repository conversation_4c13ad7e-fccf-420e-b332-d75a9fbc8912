import torch
from transformers import ViTModel, ViTImageProcessor
from PIL import Image
import requests
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import os
import sys

MODEL_NAME = "google/vit-base-patch16-224-in21k"
LOCAL_MODEL_PATH = "./vit-model-local"


class ImageUtils:
    def __init__(self, model_name: str = "google/vit-base-patch16-224-in21k", model_path: str = "./vit-model-local"):
        self.MODEL_NAME = model_name
        self.MODEL_PATH = model_path
        self.model_vit = None
        self.process_vit = None
        self.load_model_and_processor()
        self.device = "cpu"
    def load_model_and_processor(self):
        print(f"--- Start the process of downloading the model '{self.MODEL_NAME}' ---")
        if os.path.isdir(self.MODEL_PATH):
            print(f"Detect local folder at '{self.MODEL_PATH}'. are downloading the model from here ...")
            try:
                self.processor = ViTImageProcessor.from_pretrained(self.MODEL_PATH)
                self.model = ViTModel.from_pretrained(self.MODEL_PATH)
                print("Tải model và processor từ thư mục cục bộ thành công.")
            except Exception as e:
                print(f"Lỗi khi tải model từ thư mục cục bộ: {e}")
                print("Sẽ thử tải lại từ Hugging Face Hub.")
        print(f"Không tìm thấy model cục bộ. Đang thử tải từ Hugging Face Hub...")
        try:
            # Tải processor và model
            self.processor = ViTImageProcessor.from_pretrained(self.MODEL_NAME)
            self.model = ViTModel.from_pretrained(self.MODEL_NAME)
            print("Tải model và processor từ Hugging Face thành công.")

            # Lưu vào thư mục cục bộ để lần sau dùng
            print(f"Đang lưu model vào thư mục '{self.MODEL_PATH}' để sử dụng sau này...")
            self.processor.save_pretrained(self.MODEL_PATH)
            self.model.save_pretrained(self.MODEL_PATH)
            print("Lưu model vào thư mục cục bộ thành công.")
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.model.to(self.device)
            self.model.eval()

        except OSError as e:
            print("\n" + "=" * 50)
            print("LỖI KẾT NỐI: KHÔNG THỂ TẢI MODEL TỪ HUGGING FACE")
            print("=" * 50)
            print(f"Chi tiết lỗi: {e}")
            print("\nVui lòng kiểm tra lại kết nối Internet hoặc cấu hình tường lửa/proxy của bạn.")
            print("HƯỚNG DẪN DỰ PHÒNG: Bạn có thể tải model thủ công:")
            print(f"1. Truy cập: https://huggingface.co/{self.MODEL_NAME}/tree/main")
            print(
                f"2. Tải các file cần thiết (config.json, pytorch_model.bin, preprocessor_config.json) vào thư mục '{self.MODEL_PATH}'.")
            print("3. Chạy lại script này.")
            return None, None
        except Exception as e:
            print(f"Đã xảy ra lỗi không xác định khi tải model: {e}")
            return None, None

    def get_image_embedding(self, image: Image.Image, device="cpu") -> np.ndarray:
        """Trích xuất vector đặc trưng từ ảnh."""
        inputs = self.processor(images=image, return_tensors="pt").to(device)
        with torch.no_grad():
            outputs = self.model(**inputs)
        embedding = outputs.last_hidden_state[:, 0, :].cpu().numpy()
        return embedding

    def open_image(self, source: str) -> Image.Image:
        if source.startswith('http'):
            try:
                return Image.open(requests.get(source, stream=True).raw).convert("RGB")
            except requests.exceptions.RequestException as e:
                return None
        else:
            return Image.open(source).convert("RGB")

    def process(self, image_compare, image_red):
        img1 = self.open_image(image_compare)
        img2 = self.open_image(image_red)
        if img1 is None or img2 is None:
            return 1.0

        # Lấy vector đặc trưng, truyền model và processor đã tải
        embedding1 = self.get_image_embedding(img1, self.device)
        embedding2 = self.get_image_embedding(img2, self.device)

        sim_score = cosine_similarity(embedding1, embedding2)[0][0]
        return sim_score
